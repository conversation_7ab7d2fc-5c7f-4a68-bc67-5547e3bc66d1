/// Location Restriction Service
///
/// Service for handling location update restrictions
/// Enforces 30-day restriction on location changes
library location_restriction_service;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/models/user_model.dart';

/// Location Restriction Service
///
/// Provides methods to validate location update restrictions
/// and calculate remaining restriction time
class LocationRestrictionService {
  /// Duration of location update restriction (30 days)
  static const Duration _restrictionDuration = Duration(days: 30);

  /// Check if user can update location
  ///
  /// Returns true if user can update location (no restriction or restriction expired)
  /// Returns false if user is still within 30-day restriction period
  ///
  /// @param userLocation Current user location with timestamp
  /// @return True if location can be updated, false otherwise
  static bool canUpdateLocation(UserLocation? userLocation) {
    if (userLocation?.updatedAt == null) {
      // No previous update timestamp, allow update
      return true;
    }

    final lastUpdate = userLocation!.updatedAt!;
    final now = DateTime.now();
    final timeSinceLastUpdate = now.difference(lastUpdate);

    final canUpdate = timeSinceLastUpdate >= _restrictionDuration;

    if (kDebugMode) {
      print('LocationRestrictionService: Last update: $lastUpdate');
      print('LocationRestrictionService: Time since last update: ${timeSinceLastUpdate.inDays} days');
      print('LocationRestrictionService: Can update: $canUpdate');
    }

    return canUpdate;
  }

  /// Get remaining restriction time
  ///
  /// Returns the remaining time until location can be updated
  /// Returns null if no restriction applies
  ///
  /// @param userLocation Current user location with timestamp
  /// @return Duration remaining until update is allowed, or null if no restriction
  static Duration? getRemainingRestrictionTime(UserLocation? userLocation) {
    if (userLocation?.updatedAt == null) {
      // No previous update timestamp, no restriction
      return null;
    }

    final lastUpdate = userLocation!.updatedAt!;
    final now = DateTime.now();
    final timeSinceLastUpdate = now.difference(lastUpdate);

    if (timeSinceLastUpdate >= _restrictionDuration) {
      // Restriction period has passed
      return null;
    }

    final remainingTime = _restrictionDuration - timeSinceLastUpdate;

    if (kDebugMode) {
      print('LocationRestrictionService: Remaining restriction time: ${remainingTime.inDays} days');
    }

    return remainingTime;
  }

  /// Get remaining days until location can be updated
  ///
  /// Returns the number of days remaining in the restriction period
  /// Returns 0 if no restriction applies
  ///
  /// @param userLocation Current user location with timestamp
  /// @return Number of days remaining in restriction period
  static int getRemainingDays(UserLocation? userLocation) {
    final remainingTime = getRemainingRestrictionTime(userLocation);
    if (remainingTime == null) {
      return 0;
    }

    // Add 1 to show at least 1 day if there's any time remaining
    return remainingTime.inDays + (remainingTime.inHours % 24 > 0 ? 1 : 0);
  }

  /// Get formatted restriction message
  ///
  /// Returns a formatted message showing remaining restriction time
  /// Returns null if no restriction applies
  ///
  /// @param userLocation Current user location with timestamp
  /// @return Formatted restriction message in Arabic, or null if no restriction
  static String? getRestrictionMessage(UserLocation? userLocation) {
    final remainingDays = getRemainingDays(userLocation);
    if (remainingDays == 0) {
      return null;
    }

    if (remainingDays == 1) {
      return 'يمكنك تغيير الموقع بعد يوم واحد';
    } else {
      return 'يمكنك تغيير الموقع بعد $remainingDays أيام';
    }
  }

  /// Check if this is the first location update
  ///
  /// Returns true if user has never set a location before
  ///
  /// @param userLocation Current user location
  /// @return True if this is the first location update
  static bool isFirstLocationUpdate(UserLocation? userLocation) {
    return userLocation == null || 
           (userLocation.lat == null && 
            userLocation.lng == null && 
            userLocation.updatedAt == null);
  }
}
