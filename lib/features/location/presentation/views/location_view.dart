/// Location View (Riverpod Version)
///
/// Riverpod version of the location selection screen following MVVM pattern
/// Demonstrates migration from GetX to Riverpod for location functionality
library location_view_riverpod;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/location/domain/services/location_restriction_service.dart';
import 'package:towasl/features/location/presentation/providers/location_provider.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/services/district_service.dart'
    as district_service;
import 'package:towasl/shared/widgets/bottom_action_bar.dart';

/// Location View (Riverpod Version)
///
/// Screen for setting user location during onboarding
/// Follows MVVM pattern with Riverpod providers instead of GetX
class LocationView extends ConsumerStatefulWidget {
  /// Whether this view is accessed from settings
  final bool isFromSetting;

  /// Whether this view is accessed from edit mode
  final bool isFromEdit;

  /// Creates a LocationView
  const LocationView({
    super.key,
    this.isFromSetting = false,
    this.isFromEdit = false,
  });

  @override
  ConsumerState<LocationView> createState() => _LocationViewState();
}

/// State for the LocationView
class _LocationViewState extends ConsumerState<LocationView> {
  @override
  void initState() {
    super.initState();
    // Load user location data when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeLocation();
    });
  }

  /// Initialize location data
  void _initializeLocation() async {
    // Try to get user ID from multiple sources
    String userId = '';

    // First try from userIdProvider
    try {
      userId = ref.read(userIdProvider);
    } catch (e) {
      if (kDebugMode) {
        print('LocationView: Could not get user ID from userIdProvider - $e');
      }
    }

    // If still empty, try from storage service directly
    if (userId.isEmpty) {
      try {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();
        if (kDebugMode) {
          print('LocationView: Got user ID from storage: $userId');
        }
      } catch (e) {
        if (kDebugMode) {
          print('LocationView: Could not get user ID from storage - $e');
        }
      }
    }

    // Load user location if we have a user ID
    if (userId.isNotEmpty) {
      if (kDebugMode) {
        print('LocationView: Loading user location for: $userId');
      }
      ref.read(locationProvider.notifier).loadUserLocation(userId);
    } else {
      if (kDebugMode) {
        print('LocationView: No user ID found, skipping user location loading');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final locationState = ref.watch(locationProvider);
    final isLoading = ref.watch(isLocationLoadingProvider);
    final isSaving = ref.watch(isLocationSavingProvider);
    final hasLocationData = ref.watch(hasLocationDataProvider);
    final hasPermission = ref.watch(hasLocationPermissionProvider);
    final userState = ref.watch(userProvider);

    // Check location update restrictions when coming from settings
    final canUpdateLocation = widget.isFromSetting
        ? LocationRestrictionService.canUpdateLocation(
            userState.userModel?.userLocation)
        : true; // Allow updates during onboarding

    final restrictionMessage = widget.isFromSetting
        ? LocationRestrictionService.getRestrictionMessage(
            userState.userModel?.userLocation)
        : null;

    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Positioned.fill(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header section
                    _buildHeader(),

                    const SizedBox(height: 25),

                    // Location card
                    _buildLocationCard(locationState, hasLocationData),

                    const SizedBox(height: 25),

                    // District selection (if city supports districts)
                    if (hasLocationData && locationState.citySupportsDistricts)
                      _buildDistrictSelection(locationState),

                    if (hasLocationData && locationState.citySupportsDistricts)
                      const SizedBox(height: 25),

                    // Action buttons
                    _buildActionButtons(hasPermission, isLoading),

                    // Location restriction message (only when from settings and restricted)
                    if (widget.isFromSetting && restrictionMessage != null) ...[
                      const SizedBox(height: 16),
                      _buildRestrictionMessage(restrictionMessage),
                    ],

                    const SizedBox(height: 25),
                  ],
                ),
              ),
            ),

            // Loading overlay
            if (isSaving)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: BottomActionBar(
        buttonText: AppLocalizations.of(context).continueText,
        isEnabled: hasLocationData && canUpdateLocation,
        isLoading: isSaving,
        onPressed: hasLocationData && canUpdateLocation
            ? () {
                if (widget.isFromSetting) {
                  _showLocationUpdateConfirmationDialog();
                } else {
                  ref.read(locationProvider.notifier).saveLocation(
                        isFromSetting: widget.isFromSetting,
                      );
                }
              }
            : canUpdateLocation
                ? null
                : () {
                    // Show error message when trying to update within restriction period
                    _showLocationUpdateFailedDialog();
                  },
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryPurple,
      elevation: 0,
      automaticallyImplyLeading: false,
      title: Text(
        AppLocalizations.of(context).homeDistrict,
        style: const TextStyle(
          color: AppColors.whitePure,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      // Show back button when coming from settings/profile
      leading: widget.isFromSetting
          ? IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: AppColors.whitePure,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            )
          : null,
    );
  }

  /// Build header section
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).pleaseEnableGpsAndAllowLocation,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.blackAsh,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  /// Build location card
  Widget _buildLocationCard(LocationState locationState, bool hasLocationData) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.whitePure,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              hasLocationData ? AppColors.primaryPurple : AppColors.greyLight,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          // Location icon (on the right in RTL)
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: hasLocationData
                  ? AppColors.primaryPurple.withOpacity(0.1)
                  : AppColors.greyLight.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              hasLocationData ? Icons.location_on : Icons.location_off,
              color: hasLocationData
                  ? AppColors.primaryPurple
                  : AppColors.greyMedium,
              size: 30,
            ),
          ),

          const SizedBox(width: 16),

          // Location text (on the left in RTL, but aligned right)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (hasLocationData) ...[
                  if (locationState.district.isNotEmpty) ...[
                    Text(
                      locationState.district,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.greyDark,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    const SizedBox(height: 4),
                  ],
                  Text(
                    locationState.city.isNotEmpty
                        ? locationState.city
                        : AppLocalizations.of(context).unknownCity,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.greyMedium,
                    ),
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    locationState.country.isNotEmpty
                        ? locationState.country
                        : AppLocalizations.of(context).unknownCountry,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.greyMedium,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ] else ...[
                  Text(
                    AppLocalizations.of(context).gpsLocationRequired,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: AppColors.greyMedium,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build district selection
  Widget _buildDistrictSelection(LocationState locationState) {
    return _SearchableDistrictSelection(
      locationState: locationState,
      onDistrictSelected: (district_service.District district) {
        ref.read(locationProvider.notifier).selectDistrict(district);
      },
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(bool hasPermission, bool isLoading) {
    return Column(
      children: [
        // GPS activation button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: isLoading
                ? null
                : () {
                    ref.read(locationProvider.notifier).getCurrentLocation();
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPurple,
              foregroundColor: AppColors.whitePure,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.whitePure),
                    ),
                  )
                : const Icon(Icons.gps_fixed),
            label: Text(
              isLoading
                  ? AppLocalizations.of(context).gettingLocation
                  : AppLocalizations.of(context).getLocation,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),

        // Permission info
        if (!hasPermission) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              border: Border.all(color: Colors.orange.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context).locationPermissionRequired,
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build restriction message widget
  Widget _buildRestrictionMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.schedule,
            color: Colors.orange.shade600,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 14,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// Show location update confirmation dialog
  void _showLocationUpdateConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.whiteIvory,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'تأكيد تحديث الموقع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.greyDark,
            ),
            textAlign: TextAlign.center,
          ),
          content: const Text(
            'هناك قيد 30 يوم لتغيير الموقع',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.greyMedium,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          actions: [
            // Edit button (highlighted)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                foregroundColor: AppColors.whitePure,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'تعديل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Confirm button
            OutlinedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(locationProvider.notifier).saveLocation(
                      isFromSetting: widget.isFromSetting,
                    );
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.greyDark,
                side: const BorderSide(color: AppColors.greyMedium),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'تأكيد',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          actionsAlignment: MainAxisAlignment.center,
        );
      },
    );
  }

  /// Show location update failed dialog
  void _showLocationUpdateFailedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.whiteIvory,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'فشل تحديث الموقع',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          content: const Text(
            'لا يمكن تحديث الموقع في الوقت الحالي بسبب قيد الـ 30 يوم',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.greyMedium,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                foregroundColor: AppColors.whitePure,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'حسناً',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          actionsAlignment: MainAxisAlignment.center,
        );
      },
    );
  }
}

/// Searchable district selection widget
class _SearchableDistrictSelection extends StatefulWidget {
  final LocationState locationState;
  final Function(district_service.District) onDistrictSelected;

  const _SearchableDistrictSelection({
    required this.locationState,
    required this.onDistrictSelected,
  });

  @override
  State<_SearchableDistrictSelection> createState() =>
      _SearchableDistrictSelectionState();
}

class _SearchableDistrictSelectionState
    extends State<_SearchableDistrictSelection> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<district_service.District> _filteredDistricts = [];
  bool _showSuggestions = false;
  String _lastSelectedDistrict = '';

  @override
  void initState() {
    super.initState();
    _filteredDistricts = widget.locationState.availableDistricts;

    // Set initial value if district is already selected
    if (widget.locationState.district.isNotEmpty) {
      _searchController.text = widget.locationState.district;
      _lastSelectedDistrict = widget.locationState.district;
    }

    _searchController.addListener(_onSearchChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(_SearchableDistrictSelection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update the search field when the location state changes
    if (oldWidget.locationState.district != widget.locationState.district) {
      if (widget.locationState.district.isNotEmpty) {
        _searchController.text = widget.locationState.district;
        _lastSelectedDistrict = widget.locationState.district;
      }
    }

    // Update filtered districts when available districts change
    if (oldWidget.locationState.availableDistricts !=
        widget.locationState.availableDistricts) {
      _filteredDistricts = widget.locationState.availableDistricts;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      if (query.isEmpty) {
        _filteredDistricts = widget.locationState.availableDistricts;
      } else {
        _filteredDistricts = widget.locationState.availableDistricts
            .where((district) =>
                district.nameAr.toLowerCase().contains(query) ||
                district.nameEn.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  void _onFocusChanged() {
    setState(() {
      if (_focusNode.hasFocus) {
        // When gaining focus, show suggestions
        _showSuggestions = true;
      } else {
        // When losing focus, hide suggestions and restore last selected if no valid selection
        _showSuggestions = false;
        // Use a post-frame callback to ensure the focus loss is processed after any tap events
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _handleFocusLoss();
        });
      }
    });
  }

  void _handleFocusLoss() {
    final currentText = _searchController.text.trim();

    // Check if current text matches any district
    final matchingDistricts = widget.locationState.availableDistricts
        .where((district) =>
            district.nameAr == currentText || district.nameEn == currentText)
        .toList();

    if (matchingDistricts.isEmpty) {
      // No valid district selected, restore last selected district
      setState(() {
        _searchController.text = _lastSelectedDistrict;
      });
    } else if (matchingDistricts.isNotEmpty) {
      // Valid district found, update the last selected
      _lastSelectedDistrict = currentText;
    }
  }

  void _selectDistrict(district_service.District district) {
    setState(() {
      _searchController.text = district.nameAr;
      _lastSelectedDistrict = district.nameAr;
      _showSuggestions = false;
    });
    _focusNode.unfocus();
    widget.onDistrictSelected(district);
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _filteredDistricts = widget.locationState.availableDistricts;
      _showSuggestions = true; // Show list when clearing
    });
    _focusNode.requestFocus(); // Keep focus on the field
  }

  void _onFieldTapped() {
    if (_searchController.text.isNotEmpty) {
      // If field has text, clear it and show all districts
      setState(() {
        _searchController.clear();
        _filteredDistricts = widget.locationState.availableDistricts;
        _showSuggestions = true;
      });
    } else {
      // If field is empty, just show suggestions
      setState(() {
        _showSuggestions = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Tap outside the search field - unfocus to trigger focus loss handling
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.whitePure,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.greyLight,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // District selection header
            Row(
              children: [
                const Icon(
                  Icons.location_city,
                  color: AppColors.primaryPurple,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).districtSelection,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.greyDark,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // District selection content
            if (widget.locationState.isLoadingDistricts) ...[
              // Loading state
              const Center(
                child: CircularProgressIndicator(),
              ),
            ] else if (widget.locationState.availableDistricts.isEmpty) ...[
              // No districts available
              Text(
                AppLocalizations.of(context).noDistrictsAvailable,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.greyMedium,
                ),
              ),
            ] else ...[
              // District search field
              Text(
                AppLocalizations.of(context).pleaseSelectDistrict,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.greyMedium,
                ),
              ),
              const SizedBox(height: 12),

              // Search text field
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _focusNode.hasFocus
                        ? AppColors.primaryPurple
                        : AppColors.greyLight,
                    width: _focusNode.hasFocus ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _searchController,
                  focusNode: _focusNode,
                  textAlign: TextAlign.right,
                  onTap: _onFieldTapped,
                  style: const TextStyle(
                    color: AppColors.greyDark,
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                    hintText: AppLocalizations.of(context).searchDistricts,
                    hintStyle: const TextStyle(
                      color: AppColors.greyMedium,
                      fontSize: 16,
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: AppColors.greyMedium,
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(
                              Icons.clear,
                              color: AppColors.greyMedium,
                            ),
                            onPressed: _clearSearch,
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),

              // Search suggestions
              if (_showSuggestions && _filteredDistricts.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  decoration: BoxDecoration(
                    color: AppColors.whitePure,
                    border: Border.all(color: AppColors.greyLight),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: _filteredDistricts.length,
                    separatorBuilder: (context, index) => const Divider(
                      height: 1,
                      color: AppColors.greyLight,
                    ),
                    itemBuilder: (context, index) {
                      final district = _filteredDistricts[index];
                      return ListTile(
                        title: Text(
                          district.nameAr,
                          style: const TextStyle(
                            color: AppColors.greyDark,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.right,
                        ),
                        subtitle: Text(
                          district.nameEn,
                          style: const TextStyle(
                            color: AppColors.greyMedium,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.right,
                        ),
                        onTap: () => _selectDistrict(district),
                        trailing: const Icon(
                          Icons.location_on,
                          color: AppColors.primaryPurple,
                          size: 20,
                        ),
                      );
                    },
                  ),
                ),
              ],

              // No results message
              if (_showSuggestions &&
                  _filteredDistricts.isEmpty &&
                  _searchController.text.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border.all(color: AppColors.greyLight),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    AppLocalizations.of(context).noDistrictsFound,
                    style: const TextStyle(
                      color: AppColors.greyMedium,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
